"""
Models Router for SmallDoge WebUI
Handles AI model management and configuration
Based on open-webui models router structure
"""

import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel

from smalldoge_webui.models.models import (
    Models, ModelModel, ModelResponse, ModelCreate, ModelUpdate
)
from smalldoge_webui.utils.auth import get_current_user, get_admin_user, get_verified_user
from smalldoge_webui.constants import ERROR_MESSAGES, SUCCESS_MESSAGES, MODEL_TYPES
from smalldoge_webui.env import SRC_LOG_LEVELS

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MODELS"])

router = APIRouter()

class ModelListResponse(BaseModel):
    """Model list response"""
    models: List[ModelResponse]
    total: int
    page: int
    per_page: int

class ModelStatusResponse(BaseModel):
    """Model status response"""
    model_id: str
    status: str
    loaded: bool
    memory_usage: Optional[float] = None
    device: Optional[str] = None

@router.get("/", response_model=ModelListResponse)
async def get_models(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    model_type: Optional[str] = Query(None),
    current_user = Depends(get_verified_user)
):
    """Get list of available models"""
    try:
        # Get models based on user role
        if current_user.role == "admin":
            models = Models.get_models(skip=skip, limit=limit, model_type=model_type)
            total = Models.get_models_count(model_type=model_type)
        else:
            # Regular users can only see public models or their own models
            models = Models.get_models(
                skip=skip, limit=limit, user_id=current_user.id, model_type=model_type
            )
            total = Models.get_models_count(user_id=current_user.id, model_type=model_type)
        
        return ModelListResponse(
            models=[ModelResponse.model_validate(model) for model in models],
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
        
    except Exception as e:
        log.error(f"Error getting models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.get("/types")
async def get_model_types(current_user = Depends(get_verified_user)):
    """Get available model types"""
    return {
        "types": [
            {"id": MODEL_TYPES.TRANSFORMERS, "name": "Transformers", "description": "HuggingFace Transformers models"},
            {"id": MODEL_TYPES.HUGGINGFACE, "name": "HuggingFace", "description": "HuggingFace Hub models"},
            {"id": MODEL_TYPES.LOCAL, "name": "Local", "description": "Local model files"},
            {"id": MODEL_TYPES.OPENAI, "name": "OpenAI", "description": "OpenAI API models"},
            {"id": MODEL_TYPES.OLLAMA, "name": "Ollama", "description": "Ollama models"}
        ]
    }

@router.post("/", response_model=ModelResponse)
async def create_model(
    model_data: ModelCreate,
    current_user = Depends(get_verified_user)
):
    """Create a new model configuration"""
    try:
        # Check if model ID already exists
        existing_model = Models.get_model_by_id(model_data.id)
        if existing_model:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=ERROR_MESSAGES.MODEL_ID_TAKEN
            )
        
        # Create model
        model = Models.create_model(model_data, current_user.id)
        if not model:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ERROR_MESSAGES.DEFAULT("Failed to create model")
            )
        
        log.info(f"Model {model_data.id} created by user {current_user.email}")
        return ModelResponse.model_validate(model)
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error creating model: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.get("/{model_id}", response_model=ModelResponse)
async def get_model_by_id(
    model_id: str,
    current_user = Depends(get_verified_user)
):
    """Get model by ID"""
    try:
        model = Models.get_model_by_id(model_id)
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND
            )
        
        # Check access permissions
        if (current_user.role != "admin" and
            model.created_by != current_user.id and
            not model.is_public):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ERROR_MESSAGES.ACCESS_PROHIBITED
            )
        
        return ModelResponse.model_validate(model)
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error getting model by ID: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.put("/{model_id}", response_model=ModelResponse)
async def update_model_by_id(
    model_id: str,
    model_data: ModelUpdate,
    current_user = Depends(get_verified_user)
):
    """Update model by ID"""
    try:
        # Check if model exists
        existing_model = Models.get_model_by_id(model_id)
        if not existing_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND
            )
        
        # Check permissions
        if (current_user.role != "admin" and
            existing_model.created_by != current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ERROR_MESSAGES.ACCESS_PROHIBITED
            )
        
        # Update model
        updated_model = Models.update_model_by_id(model_id, model_data)
        if not updated_model:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ERROR_MESSAGES.DEFAULT("Failed to update model")
            )
        
        log.info(f"Model {model_id} updated by user {current_user.email}")
        return ModelResponse.model_validate(updated_model)
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error updating model: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.delete("/{model_id}")
async def delete_model_by_id(
    model_id: str,
    current_user = Depends(get_verified_user)
):
    """Delete model by ID"""
    try:
        # Check if model exists
        existing_model = Models.get_model_by_id(model_id)
        if not existing_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND
            )
        
        # Check permissions
        if (current_user.role != "admin" and
            existing_model.created_by != current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ERROR_MESSAGES.ACCESS_PROHIBITED
            )
        
        # Delete model
        success = Models.delete_model_by_id(model_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ERROR_MESSAGES.DEFAULT("Failed to delete model")
            )
        
        log.info(f"Model {model_id} deleted by user {current_user.email}")
        return {"message": SUCCESS_MESSAGES.MODEL_DELETED}
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error deleting model: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.post("/{model_id}/load")
async def load_model(
    model_id: str,
    current_user = Depends(get_verified_user)
):
    """Load model into memory for inference"""
    try:
        # Check if model exists
        model = Models.get_model_by_id(model_id)
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND
            )
        
        # Check access permissions
        if (current_user.role != "admin" and
            model.created_by != current_user.id and
            not model.is_public):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ERROR_MESSAGES.ACCESS_PROHIBITED
            )
        
        # Load model using inference engine
        from smalldoge_webui.utils.inference import ModelInferenceEngine
        inference_engine = ModelInferenceEngine()
        
        success = await inference_engine.load_model(model_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ERROR_MESSAGES.MODEL_LOADING_ERROR
            )
        
        log.info(f"Model {model_id} loaded by user {current_user.email}")
        return {"message": f"Model {model_id} loaded successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error loading model: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.post("/{model_id}/unload")
async def unload_model(
    model_id: str,
    current_user = Depends(get_verified_user)
):
    """Unload model from memory"""
    try:
        # Unload model using inference engine
        from smalldoge_webui.utils.inference import ModelInferenceEngine
        inference_engine = ModelInferenceEngine()
        
        success = await inference_engine.unload_model(model_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ERROR_MESSAGES.DEFAULT("Failed to unload model")
            )
        
        log.info(f"Model {model_id} unloaded by user {current_user.email}")
        return {"message": f"Model {model_id} unloaded successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error unloading model: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.get("/{model_id}/status", response_model=ModelStatusResponse)
async def get_model_status(
    model_id: str,
    current_user = Depends(get_verified_user)
):
    """Get model loading status"""
    try:
        # Check if model exists
        model = Models.get_model_by_id(model_id)
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND
            )
        
        # Get status from inference engine
        from smalldoge_webui.utils.inference import ModelInferenceEngine
        inference_engine = ModelInferenceEngine()
        
        status_info = await inference_engine.get_model_status(model_id)
        
        return ModelStatusResponse(
            model_id=model_id,
            status=status_info.get("status", "unknown"),
            loaded=status_info.get("loaded", False),
            memory_usage=status_info.get("memory_usage"),
            device=status_info.get("device")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error getting model status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )
