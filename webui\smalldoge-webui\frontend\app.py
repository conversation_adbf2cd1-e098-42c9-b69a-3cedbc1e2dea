"""
SmallDoge WebUI Frontend
Gradio-based frontend for SmallDoge WebUI backend
"""

import gradio as gr
import requests
import json
import os
from typing import Optional, Dict, Any, List, Tuple
import time

# Backend API configuration
BACKEND_URL = os.getenv("BACKEND_URL", "http://localhost:8000")
API_BASE = f"{BACKEND_URL}/api/v1"

class SmallDogeAPI:
    """API client for SmallDoge WebUI backend"""
    
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.user_info = None
    
    def set_token(self, token: str):
        """Set authentication token"""
        self.token = token
        self.session.headers.update({"Authorization": f"Bearer {token}"})
    
    def clear_token(self):
        """Clear authentication token"""
        self.token = None
        self.user_info = None
        if "Authorization" in self.session.headers:
            del self.session.headers["Authorization"]
    
    def signup(self, name: str, email: str, password: str) -> Tuple[bool, str]:
        """User signup"""
        try:
            response = self.session.post(f"{API_BASE}/auth/signup", json={
                "name": name,
                "email": email,
                "password": password
            })

            if response.status_code == 200:
                data = response.json()
                # Check if response contains token and user info
                if "token" in data and "user" in data:
                    self.set_token(data["token"])
                    self.user_info = data["user"]
                    return True, "注册成功！"
                else:
                    # If no token in signup response, try to sign in
                    return self.signin(email, password)
            else:
                error_msg = response.json().get("detail", "注册失败")
                return False, f"注册失败: {error_msg}"

        except Exception as e:
            return False, f"网络错误: {str(e)}"
    
    def signin(self, email: str, password: str) -> Tuple[bool, str]:
        """User signin"""
        try:
            response = self.session.post(f"{API_BASE}/auth/signin", json={
                "email": email,
                "password": password
            })
            
            if response.status_code == 200:
                data = response.json()
                self.set_token(data["token"])
                self.user_info = data["user"]
                return True, "登录成功！"
            else:
                error_msg = response.json().get("detail", "登录失败")
                return False, f"登录失败: {error_msg}"
                
        except Exception as e:
            return False, f"网络错误: {str(e)}"
    
    def get_models(self) -> List[Dict[str, Any]]:
        """Get available models"""
        try:
            response = self.session.get(f"{API_BASE}/models")
            if response.status_code == 200:
                return response.json().get("models", [])
            return []
        except Exception as e:
            print(f"Error getting models: {e}")
            return []
    
    def chat_completion(self, model: str, messages: List[Dict[str, str]], stream: bool = True):
        """Chat completion with streaming support"""
        try:
            payload = {
                "model": model,
                "messages": messages,
                "stream": stream,
                "temperature": 0.7,
                "max_tokens": 2048
            }

            print(f"Chat completion request:")  # Debug info
            print(f"  Model: {model}")
            print(f"  Messages: {messages}")
            print(f"  Stream: {stream}")
            print(f"  Payload: {payload}")
            
            if stream:
                response = self.session.post(
                    f"{API_BASE}/inference/chat/completions",
                    json=payload,
                    stream=True
                )
                
                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            line = line.decode('utf-8')
                            if line.startswith('data: '):
                                data = line[6:]
                                if data == '[DONE]':
                                    break
                                try:
                                    chunk = json.loads(data)
                                    if 'choices' in chunk and chunk['choices']:
                                        delta = chunk['choices'][0].get('delta', {})
                                        content = delta.get('content', '')
                                        if content:
                                            yield content
                                except json.JSONDecodeError:
                                    continue
                else:
                    yield f"错误: {response.status_code}"
            else:
                response = self.session.post(
                    f"{API_BASE}/inference/chat/completions",
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'choices' in data and data['choices']:
                        yield data['choices'][0]['message']['content']
                else:
                    yield f"错误: {response.status_code}"
                    
        except Exception as e:
            yield f"网络错误: {str(e)}"

# Global API client
api = SmallDogeAPI()

def check_auth_status():
    """Check if user is authenticated"""
    return api.token is not None

def handle_signup(name: str, email: str, password: str, confirm_password: str):
    """Handle user signup"""
    if not name or not email or not password:
        return False, "请填写所有字段", gr.update(), gr.update()
    
    if password != confirm_password:
        return False, "密码确认不匹配", gr.update(), gr.update()
    
    if len(password) < 8:
        return False, "密码长度至少8位", gr.update(), gr.update()
    
    success, message = api.signup(name, email, password)
    
    if success:
        return True, message, gr.update(visible=False), gr.update(visible=True)
    else:
        return False, message, gr.update(), gr.update()

def handle_signin(email: str, password: str):
    """Handle user signin"""
    if not email or not password:
        return False, "请填写邮箱和密码", gr.update(), gr.update()

    success, message = api.signin(email, password)

    if success:
        print(f"Login successful, token: {api.token[:20]}..." if api.token else "No token")
        return True, message, gr.update(visible=False), gr.update(visible=True)
    else:
        return False, message, gr.update(), gr.update()

def handle_signout():
    """Handle user signout"""
    api.clear_token()
    return gr.update(visible=True), gr.update(visible=False), "已退出登录"

def get_available_models():
    """Get list of available models"""
    if not check_auth_status():
        return []

    models = api.get_models()
    print(f"Available models from API: {models}")  # Debug info
    model_ids = [model.get("id", "unknown") for model in models]
    print(f"Model IDs: {model_ids}")  # Debug info
    return model_ids

def chat_with_model(message: str, history: List[Dict[str, str]], model: str):
    """Chat with selected model"""
    if not check_auth_status():
        return history, ""

    if not message.strip():
        return history, ""

    if not model:
        history.append({"role": "user", "content": message})
        history.append({"role": "assistant", "content": "请先选择一个模型"})
        return history, ""

    # Add user message to history
    history.append({"role": "user", "content": message})

    # Prepare messages for API (use history directly since it's already in the right format)
    messages = [msg for msg in history if msg.get("content")]

    # Add assistant placeholder
    history.append({"role": "assistant", "content": ""})

    # Stream response
    response_text = ""
    try:
        for chunk in api.chat_completion(model, messages, stream=True):
            response_text += chunk
            history[-1]["content"] = response_text
            yield history, ""
    except Exception as e:
        history[-1]["content"] = f"错误: {str(e)}"
        yield history, ""

    return history, ""

def get_model_info(model: str):
    """Get information about selected model"""
    if not model:
        return "请选择一个模型"

    return f"""
**模型**: {model}
**状态**: 可用
**类型**: Transformers
**描述**: 基于 HuggingFace Transformers 的语言模型
    """

def create_model(model_id: str, model_name: str, model_path: str, model_type: str):
    """Create a new model configuration"""
    if not check_auth_status():
        return False, "请先登录"

    if not all([model_id, model_name, model_path, model_type]):
        return False, "请填写所有字段"

    print(f"Creating model with auth token: {api.token[:20]}..." if api.token else "No token")
    print(f"Session headers: {api.session.headers}")

    try:
        response = api.session.post(f"{API_BASE}/models", json={
            "id": model_id,
            "name": model_name,
            "description": f"{model_name} - {model_type} model",
            "model_type": model_type,
            "model_path": model_path,
            "config": {
                "max_length": 2048,
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "do_sample": True,
                "load_in_8bit": False,
                "load_in_4bit": False,
                "device_map": "auto",
                "torch_dtype": "auto",
                "trust_remote_code": False
            },
            "is_public": True
        })

        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")

        if response.status_code == 200 or response.status_code == 201:
            return True, "模型创建成功！"
        else:
            try:
                error_msg = response.json().get("detail", "创建失败")
            except:
                error_msg = f"HTTP {response.status_code}: {response.text}"
            return False, f"创建失败: {error_msg}"

    except Exception as e:
        return False, f"网络错误: {str(e)}"

# Create Gradio interface
def create_app():
    """Create Gradio application"""
    
    with gr.Blocks(
        title="SmallDoge WebUI",
        theme=gr.themes.Soft(),
        css="""
        .main-container { max-width: 1200px; margin: 0 auto; }
        .auth-container { max-width: 400px; margin: 0 auto; padding: 2rem; }
        .chat-container { height: 600px; }
        """
    ) as app:
        
        gr.Markdown("# 🐕 SmallDoge WebUI", elem_classes=["main-container"])
        gr.Markdown("基于 Transformers 的 AI 模型推理平台", elem_classes=["main-container"])
        
        # Authentication section
        with gr.Column(visible=True, elem_classes=["auth-container"]) as auth_section:
            gr.Markdown("## 用户认证")
            
            with gr.Tabs():
                with gr.Tab("登录"):
                    signin_email = gr.Textbox(label="邮箱", placeholder="请输入邮箱")
                    signin_password = gr.Textbox(label="密码", type="password", placeholder="请输入密码")
                    signin_btn = gr.Button("登录", variant="primary")
                    signin_msg = gr.Markdown("")
                
                with gr.Tab("注册"):
                    signup_name = gr.Textbox(label="姓名", placeholder="请输入姓名")
                    signup_email = gr.Textbox(label="邮箱", placeholder="请输入邮箱")
                    signup_password = gr.Textbox(label="密码", type="password", placeholder="请输入密码")
                    signup_confirm = gr.Textbox(label="确认密码", type="password", placeholder="请再次输入密码")
                    signup_btn = gr.Button("注册", variant="primary")
                    signup_msg = gr.Markdown("")
        
        # Main application section
        with gr.Column(visible=False, elem_classes=["main-container"]) as main_section:

            # Header with user info and signout
            with gr.Row():
                gr.Markdown("## 🤖 SmallDoge WebUI")
                signout_btn = gr.Button("退出登录", size="sm")

            # Main tabs
            with gr.Tabs():
                # Chat tab
                with gr.Tab("💬 对话"):
                    # Model selection
                    with gr.Row():
                        model_dropdown = gr.Dropdown(
                            label="选择模型",
                            choices=[],
                            value=None,
                            interactive=True,
                            allow_custom_value=True
                        )
                        refresh_models_btn = gr.Button("刷新模型", size="sm")

                    # Chat interface
                    with gr.Row():
                        with gr.Column(scale=4):
                            chatbot = gr.Chatbot(
                                label="对话",
                                height=500,
                                type="messages",
                                elem_classes=["chat-container"]
                            )

                            with gr.Row():
                                msg_input = gr.Textbox(
                                    label="消息",
                                    placeholder="输入您的消息...",
                                    scale=4
                                )
                                send_btn = gr.Button("发送", variant="primary", scale=1)

                        with gr.Column(scale=1):
                            gr.Markdown("### 对话设置")
                            clear_btn = gr.Button("清空对话")

                            gr.Markdown("### 模型信息")
                            model_info = gr.Markdown("请选择一个模型")

                # Model management tab
                with gr.Tab("🔧 模型管理"):
                    gr.Markdown("### 添加新模型")

                    with gr.Row():
                        with gr.Column():
                            new_model_id = gr.Textbox(label="模型ID", placeholder="例如: gpt2-small", value="gpt2-small")
                            new_model_name = gr.Textbox(label="模型名称", placeholder="例如: GPT-2 Small", value="GPT-2 Small")
                            new_model_path = gr.Textbox(label="模型路径", placeholder="例如: gpt2", value="gpt2")
                            new_model_type = gr.Dropdown(
                                label="模型类型",
                                choices=["transformers", "huggingface", "local"],
                                value="transformers"
                            )
                            create_model_btn = gr.Button("创建模型", variant="primary")
                            create_model_msg = gr.Markdown("")

                        with gr.Column():
                            gr.Markdown("### 模型说明")
                            gr.Markdown("""
                            **模型类型说明:**
                            - **transformers**: HuggingFace Transformers 模型
                            - **huggingface**: HuggingFace Hub 模型
                            - **local**: 本地模型文件

                            **推荐模型路径:**
                            - GPT-2: `gpt2`
                            - DialoGPT: `microsoft/DialoGPT-small`
                            - Doge-160M: `SmallDoge/Doge-160M` (需要 trust_remote_code)

                            **注意**: 某些模型需要设置 trust_remote_code=True
                            """)

                # Settings tab
                with gr.Tab("⚙️ 设置"):
                    gr.Markdown("### 系统设置")

                    with gr.Row():
                        with gr.Column():
                            gr.Markdown("#### 后端配置")
                            backend_url_input = gr.Textbox(
                                label="后端URL",
                                value=BACKEND_URL,
                                interactive=False
                            )
                            backend_status = gr.Markdown("🟢 后端连接正常")

                        with gr.Column():
                            gr.Markdown("#### 用户信息")
                            user_info_display = gr.Markdown("加载中...")

                    with gr.Row():
                        test_connection_btn = gr.Button("测试连接")
                        refresh_user_info_btn = gr.Button("刷新用户信息")
        
        # Additional helper functions
        def test_backend_connection():
            """Test backend connection"""
            try:
                response = requests.get(f"{BACKEND_URL}/health", timeout=5)
                if response.status_code == 200:
                    return "🟢 后端连接正常"
                else:
                    return f"🔴 后端连接失败 (状态码: {response.status_code})"
            except Exception as e:
                return f"🔴 后端连接失败: {str(e)}"

        def get_user_info():
            """Get current user information"""
            if not check_auth_status():
                return "未登录"

            if api.user_info:
                return f"""
                **用户名**: {api.user_info.get('name', 'N/A')}
                **邮箱**: {api.user_info.get('email', 'N/A')}
                **角色**: {api.user_info.get('role', 'N/A')}
                **注册时间**: {api.user_info.get('created_at', 'N/A')}
                """
            else:
                return "用户信息加载失败"

        # Event handlers

        # Authentication events
        signin_btn.click(
            handle_signin,
            inputs=[signin_email, signin_password],
            outputs=[gr.State(), signin_msg, auth_section, main_section]
        )

        signup_btn.click(
            handle_signup,
            inputs=[signup_name, signup_email, signup_password, signup_confirm],
            outputs=[gr.State(), signup_msg, auth_section, main_section]
        )

        signout_btn.click(
            handle_signout,
            outputs=[auth_section, main_section, gr.Markdown()]
        )

        # Model management events
        refresh_models_btn.click(
            get_available_models,
            outputs=[model_dropdown]
        )

        model_dropdown.change(
            get_model_info,
            inputs=[model_dropdown],
            outputs=[model_info]
        )

        create_model_btn.click(
            create_model,
            inputs=[new_model_id, new_model_name, new_model_path, new_model_type],
            outputs=[gr.State(), create_model_msg]
        )

        # Chat events
        send_btn.click(
            chat_with_model,
            inputs=[msg_input, chatbot, model_dropdown],
            outputs=[chatbot, msg_input]
        )

        msg_input.submit(
            chat_with_model,
            inputs=[msg_input, chatbot, model_dropdown],
            outputs=[chatbot, msg_input]
        )

        clear_btn.click(
            lambda: [],
            outputs=[chatbot]
        )

        # Settings events
        test_connection_btn.click(
            test_backend_connection,
            outputs=[backend_status]
        )

        refresh_user_info_btn.click(
            get_user_info,
            outputs=[user_info_display]
        )

        # Load initial data
        app.load(
            get_available_models,
            outputs=[model_dropdown]
        )

        app.load(
            test_backend_connection,
            outputs=[backend_status]
        )
    
    return app

if __name__ == "__main__":
    app = create_app()
    app.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        debug=True
    )
